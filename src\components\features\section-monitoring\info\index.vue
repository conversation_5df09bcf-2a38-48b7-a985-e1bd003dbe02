<template>
  <div class="flex px-7.5 py-4 items-center bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <span class="text-2xl text-#9E9E9E mr-9">查询结果</span>

    <div class="flex flex-1">
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          v-model:value="timeRange"
          class="w-full"
          type="datetimerange"
          size="large"
          placeholder="请选择时间"
          clearable
        >
        </n-date-picker>
      </InputGroup>
    </div>
    <div class="flex">
      <n-button type="info" class="mr-2.5" size="large" @click="handleSearch" :loading="loading">
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        搜索
      </n-button>
      <n-button size="large" class="mr-2.5" @click="handleExport">
        <template #icon>
          <n-icon><ExportIcon /></n-icon>
        </template>
        导出表格
      </n-button>

      <n-button type="info" size="large" @click="handleBack">
        <template #icon>
          <n-icon><ChevronLeft20FilledIcon /></n-icon>
        </template>
        返回
      </n-button>
    </div>
  </div>
  <LineChart title="断面潮流曲线" :data="multiSeriesData" height="450px" class="mt-2" />

  <SectionDetail :section-data="sectionDetailData"></SectionDetail>
  <DataTable :columns="columns" :data="tableData" :loading="loading" height="calc(100vh - 670px)">
  </DataTable>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { NDatePicker, NButton, NIcon } from 'naive-ui'
import InputGroup from '@/components/shared/InputGroup.vue'

import { SearchIcon, ExportIcon, ChevronLeft20FilledIcon } from '@/utils/constant/icons'

import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/DataTable.vue'
import SectionDetail from './SectionDetail.vue'
import { useSectionMonitoringStore } from '@/stores'
import {
  SectionMonitoringService,
  type SectionStatisticDetailResponse,
  type SectionStatisticDetailParams,
} from '@/utils/api'
import type { SeriesData } from '@/types/chart'

// 响应式数据
const sectionMonitoringStore = useSectionMonitoringStore()
const loading = ref(false)
const sectionDetailData = ref<SectionStatisticDetailResponse | null>(null)

// 时间选择器数据
const timeRange = ref<[number, number] | null>(null)

// 图表数据
const multiSeriesData = computed<SeriesData[]>(() => {
  if (!sectionDetailData.value?.dataList) {
    return []
  }

  const dataList = sectionDetailData.value.dataList

  return [
    {
      name: '潮流值',
      data: dataList.map((item) => ({
        name: item.time,
        value: parseFloat(item.value) || 0,
      })),
    },
    {
      name: '限额值',
      data: dataList.map((item) => ({
        name: item.time,
        value: parseFloat(item.limit) || 0,
      })),
      color: 'rgba(230, 176, 46, 1)',
      gradientColors: {
        start: 'rgba(230, 176, 46, 0)',
        end: 'rgba(230, 176, 46, 0)',
      },
    },
  ]
})

// 定义表格列配置
const columns = ref([
  {
    key: 'time',
    title: '日期',
    sortable: false,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'overPeriod',
    title: '越限时间段',
    sortable: false,
    align: 'center' as const,
    width: '15%',
  },
  {
    key: 'limit',
    title: '限额',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'longestOverTime',
    title: '最长出现越限时长',
    sortable: true,
    align: 'center' as const,
    width: '15%',
  },
])

// 表格数据
const tableData = computed(() => {
  return sectionDetailData.value?.statisticDataList || []
})

// 获取断面统计详情数据
const fetchSectionStatisticDetail = async () => {
  if (!sectionMonitoringStore.selectedTableRow || !timeRange.value) {
    return
  }

  loading.value = true
  try {
    const params: SectionStatisticDetailParams = {
      startTime: new Date(timeRange.value[0]).toISOString(),
      endTime: new Date(timeRange.value[1]).toISOString(),
      sectionId: sectionMonitoringStore.selectedTableRow.id,
    }

    const response = await SectionMonitoringService.getSectionStatisticDetail(params)
    sectionDetailData.value = response
  } catch (error) {
    console.error('获取断面统计详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchSectionStatisticDetail()
}

// 导出处理函数
const handleExport = () => {
  console.log('导出表格')
  // 这里可以添加导出逻辑
}

const handleBack = () => {
  sectionMonitoringStore.selectedTableRow = null
}

// 监听选中行变化，自动获取数据
watch(
  () => sectionMonitoringStore.selectedTableRow,
  (newRow) => {
    if (newRow && timeRange.value) {
      fetchSectionStatisticDetail()
    }
  },
  { immediate: true },
)

// 组件挂载时设置默认时间范围
onMounted(() => {
  // 设置默认时间范围为当前月份
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
  timeRange.value = [startOfMonth.getTime(), endOfMonth.getTime()]

  // 如果有选中的行，则获取数据
  if (sectionMonitoringStore.selectedTableRow) {
    fetchSectionStatisticDetail()
  }
})
</script>
